# Mobilify Frontend

Transform your website or idea into a professional mobile app with AI-powered conversion. No code required.

## 🚀 Quick Start

### Prerequisites

- Node.js 18.x LTS
- npm (comes with Node.js)
- Firebase project (see setup guide below)

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Fill in your Firebase configuration values in `.env.local`

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

### Project Structure

```
src/
├── components/          # Reusable React components
│   ├── ui/             # Basic UI components (Button, Input, etc.)
│   ├── layout/         # Layout components (<PERSON>er, Footer)
│   ├── home/           # Homepage sections
│   ├── auth/           # Authentication components
│   └── dashboard/      # Dashboard components
├── pages/              # Next.js pages
├── lib/                # Utility functions and configurations
├── hooks/              # Custom React hooks
└── styles/             # Global styles and Tailwind CSS
```

### Tech Stack

- **Framework:** Next.js 14 with TypeScript
- **Styling:** Tailwind CSS
- **Authentication:** Firebase Auth
- **Database:** Firestore
- **Icons:** Heroicons
- **Testing:** Jest + React Testing Library
- **Analytics:** Google Analytics 4

## 🔥 Firebase Setup

1. **Create Firebase Project:**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create new project: `mobilify-dev`
   - Enable Google Analytics

2. **Enable Services:**
   - Authentication (Email/Password)
   - Firestore Database
   - Storage

3. **Get Configuration:**
   - Go to Project Settings
   - Add web app
   - Copy configuration to `.env.local`

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage
```

### Test Structure

- Unit tests for components in `__tests__` folders
- Integration tests for pages
- Test utilities in `src/lib/test-utils.ts`

## 📊 Analytics

Google Analytics 4 is integrated to track:

- Page views
- User signups and logins
- Project creation events
- CTA button clicks
- Conversion funnel metrics

## 🎨 UI/UX Guidelines

### Design System

- **Colors:** Primary blue (#3B82F6), grays, success/error states
- **Typography:** Inter font family
- **Spacing:** Tailwind's 4px grid system
- **Components:** Consistent button styles, form inputs, and layouts

### Responsive Design

- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- All components tested on mobile, tablet, and desktop

## 🔒 Security

- Firebase Auth handles authentication
- Protected routes for dashboard
- Input validation on client and server
- CORS configuration for API calls
- Rate limiting on authentication endpoints

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect to Vercel:**
   ```bash
   npm install -g vercel
   vercel
   ```

2. **Set Environment Variables:**
   - Add all `.env.local` variables to Vercel dashboard
   - Update API URLs for production

3. **Deploy:**
   ```bash
   vercel --prod
   ```

### Manual Build

```bash
npm run build
npm run start
```

## 📝 Environment Variables

### Required Variables

```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=
```

## 🤝 Contributing

1. Create feature branch: `git checkout -b feature/your-feature`
2. Make changes and test: `npm test`
3. Commit with clear message: `git commit -m "feat: add new feature"`
4. Push and create PR: `git push origin feature/your-feature`

## 📚 Documentation

- [Setup Guide](./documents/File_SETUP-GUIDE-v1.md)
- [API Documentation](./documents/File_API-DOCS-v1.md)
- [UI/UX Guidelines](./documents/File_UI-UX-GUIDE-v1.md)
- [Testing Plan](./documents/File_TESTING-PLAN-v1.md)

## 🐛 Troubleshooting

### Common Issues

1. **Firebase connection errors:**
   - Check environment variables
   - Verify Firebase project configuration

2. **Build errors:**
   - Clear `.next` folder: `rm -rf .next`
   - Reinstall dependencies: `rm -rf node_modules && npm install`

3. **Test failures:**
   - Update snapshots: `npm test -- -u`
   - Check mock configurations

## 📄 License

This project is proprietary software for Mobilify.

---

**Built with ❤️ by the Mobilify team**
