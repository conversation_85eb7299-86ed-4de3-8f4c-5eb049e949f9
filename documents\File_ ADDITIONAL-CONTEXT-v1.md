### **File: `ADDITIONAL-CONTEXT-v1.md`**

### **Description: Provides supplementary documents for specific workflows, including the project backlog, security notes, and user documentation plans.**

### **1\. Roadmap & Backlog (v1.1 and beyond)**

This section lists features and improvements planned for after the initial v1.0 launch. They are not in scope for the current development effort.

| Priority | Feature / Item | Description | Rationale |
| ----- | ----- | ----- | ----- |
| **P1** | **Payment Integration** | Integrate with <PERSON><PERSON> to handle payments for the "Starter" and "Pro" plans directly on the website. | Core to monetizing the service automatically. |
| **P1** | **Advanced Customization** | Allow users to edit more app details from the dashboard, such as the splash screen, app icon, and navigation structure. | Increases the value of the "Pro" plan and gives users more control. |
| **P2** | **Public Blog** | Add a blog (e.g., `/blog`) to publish content about app development, marketing, and customer success stories. | Drives organic traffic (SEO) and establishes <PERSON><PERSON><PERSON> as a thought leader. |
| **P2** | **Direct App Store Publishing** | Build a service that guides users through, or automates, the process of submitting their generated apps to the Apple App Store and Google Play Store. | Solves a major pain point for non-technical users. |
| **P3** | **Team Accounts** | Allow a user to invite other team members to view and manage projects. | Useful for small businesses and agencies using the service. |

### **2\. Security & Compliance Notes**

* **Authentication Flow:** The system will use a standard JWT (JSON Web Token) flow with short-lived access tokens (e.g., 15 minutes) and long-lived, securely stored refresh tokens (e.g., 7 days). The refresh token will be used to obtain a new access token without requiring the user to log in again.  
* **Threat Model:**  
  * **Primary Threat:** Credential stuffing and account takeover.  
    * **Mitigation:** Enforce strong password policies; implement rate limiting on the login endpoint.  
  * **Secondary Threat:** Insecure Direct Object Reference (IDOR), where a user could potentially access a project that isn't theirs by guessing the `projectId`.  
    * **Mitigation:** All API endpoints that accept a `projectId` (e.g., `GET /projects/:projectId`) **must** include a check to ensure the `userId` from the JWT matches the `userId` on the project document in Firestore.

### **3\. User Documentation Plan**

* **Target:** A public-facing "Help Center" or knowledge base.  
* **Initial Articles to Write:**  
  1. "How to Get Started with Mobilify"  
  2. "Customizing Your App's Look and Feel"  
  3. "How to Set Up Push Notifications"  
  4. "A Step-by-Step Guide to Publishing on the Apple App Store"  
  5. "A Step-by-Step Guide to Publishing on the Google Play Store"

