### **File: `ADDITIONAL-CONTEXT-v1.md`**

### **Description: Provides supplementary documents for specific workflows, including development setup, project backlog, security notes, and user documentation plans.**

### **0\. Development Environment Setup**

#### **Required Tools & Extensions**

**IDE Recommendation:** Visual Studio Code with the following extensions:
- **Prettier** - Code formatter (enforces CODING-STANDARDS-v1.md)
- **ESLint** - Code quality and error detection
- **Tailwind CSS IntelliSense** - Autocomplete for Tailwind classes
- **Firebase** - Firebase project management
- **GitLens** - Enhanced Git capabilities

**Package Manager:** npm (definitive choice for consistency)

#### **Git Workflow**

**Branch Strategy:**
- `main` branch: Protected, production-ready code
- `feature/*` branches: All new development (e.g., `feature/login-page`)
- `hotfix/*` branches: Critical production fixes

**Workflow:**
1. Create feature branch from `main`
2. Develop and test locally
3. Open Pull Request to `main`
4. Require code review before merge
5. Automated CI/CD deploys to production

**Commit Message Format:**
```
type(scope): description

feat(auth): add Firebase authentication setup
fix(ui): resolve mobile navigation menu issue
docs(api): update endpoint documentation
```

### **1\. Roadmap & Backlog (v1.1 and beyond)**

This section lists features and improvements planned for after the initial v1.0 launch. They are not in scope for the current development effort.

| Priority | Feature / Item | Description | Rationale |
| ----- | ----- | ----- | ----- |
| **P1** | **Payment Integration** | Integrate with Stripe to handle payments for the "Starter" and "Pro" plans directly on the website. | Core to monetizing the service automatically. |
| **P1** | **Advanced Customization** | Allow users to edit more app details from the dashboard, such as the splash screen, app icon, and navigation structure. | Increases the value of the "Pro" plan and gives users more control. |
| **P2** | **Public Blog** | Add a blog (e.g., `/blog`) to publish content about app development, marketing, and customer success stories. | Drives organic traffic (SEO) and establishes Mobilify as a thought leader. |
| **P2** | **Direct App Store Publishing** | Build a service that guides users through, or automates, the process of submitting their generated apps to the Apple App Store and Google Play Store. | Solves a major pain point for non-technical users. |
| **P3** | **Team Accounts** | Allow a user to invite other team members to view and manage projects. | Useful for small businesses and agencies using the service. |

### **2\. Analytics & Performance Monitoring**

#### **Google Analytics 4 Setup**

**DEFINITIVE REQUIREMENT:** Set up GA4 from the beginning to track conversion funnels.

**Key Events to Track:**
- `page_view` - All page visits
- `sign_up` - User account creation
- `login` - User authentication
- `project_start` - User begins project creation
- `project_complete` - Project generation finished
- `pricing_view` - Pricing page visits
- `cta_click` - Call-to-action button clicks

**Conversion Goals:**
- Primary: Visitor → Project Start (5% target)
- Secondary: Visitor → Account Creation
- Tertiary: Account Creation → Project Start

**Implementation:**
- Use `gtag` library in Next.js
- Track custom events for business metrics
- Set up conversion funnels in GA4 dashboard
- Monitor Core Web Vitals through Vercel Analytics

### **3\. Content Management Decisions**

**FAQ Content:** Hardcoded in React components (no CMS for v1.0)
**Marketing Copy:** Use exact text from PRP documents (no A/B testing for v1.0)
**Email Templates:** Customize Firebase Auth templates with Mobilify branding

### **4\. Security & Compliance Notes**

* **Authentication Flow:** The system will use a standard JWT (JSON Web Token) flow with short-lived access tokens (e.g., 15 minutes) and long-lived, securely stored refresh tokens (e.g., 7 days). The refresh token will be used to obtain a new access token without requiring the user to log in again.  
* **Threat Model:**  
  * **Primary Threat:** Credential stuffing and account takeover.  
    * **Mitigation:** Enforce strong password policies; implement rate limiting on the login endpoint.  
  * **Secondary Threat:** Insecure Direct Object Reference (IDOR), where a user could potentially access a project that isn't theirs by guessing the `projectId`.  
    * **Mitigation:** All API endpoints that accept a `projectId` (e.g., `GET /projects/:projectId`) **must** include a check to ensure the `userId` from the JWT matches the `userId` on the project document in Firestore.

### **3\. User Documentation Plan**

* **Target:** A public-facing "Help Center" or knowledge base.  
* **Initial Articles to Write:**  
  1. "How to Get Started with Mobilify"  
  2. "Customizing Your App's Look and Feel"  
  3. "How to Set Up Push Notifications"  
  4. "A Step-by-Step Guide to Publishing on the Apple App Store"  
  5. "A Step-by-Step Guide to Publishing on the Google Play Store"

