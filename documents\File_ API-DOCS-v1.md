### **File: `API-DOCS-v1.md`**

### **Description: Defines the API endpoints, request/response formats, and error codes for the Mobilify backend service.**

### **Base URL: `/api/v1`**

**DEFINITIVE CHOICE:** All API endpoints use versioned URLs for future-proofing.

### **Authentication:**

All endpoints, unless marked "Public," require an `Authorization` header with a Firebase ID Token: `Authorization: Bearer <FIREBASE_ID_TOKEN>`

**Note:** Authentication is handled entirely by Firebase Auth SDK on the client-side. The backend uses Firebase Admin SDK to verify ID tokens.

### **1\. User Profile Endpoints**

#### **`POST /api/v1/users/profile`**

* **Description:** Creates or updates user profile information after Firebase Auth signup.
* **Access:** Authenticated (Firebase ID Token required)

**Request Body:**
{
  "fullName": "<PERSON>",
  "email": "<EMAIL>"
}

**Success Response (201 \- Created):**
{
  "userId": "firebase_uid_abc123",
  "email": "<EMAIL>",
  "fullName": "<PERSON> Conner",
  "createdAt": "2025-07-12T09:00:00Z"
}

* **Error Responses:**
  * `400 Bad Request`: Invalid input data.
  * `401 Unauthorized`: Invalid or missing Firebase ID token.

#### **`GET /api/v1/users/profile`**

* **Description:** Retrieves the current user's profile information.
* **Access:** Authenticated (Firebase ID Token required)

**Success Response (200 \- OK):**
{
  "userId": "firebase_uid_abc123",
  "email": "<EMAIL>",
  "fullName": "Sarah Conner",
  "createdAt": "2025-07-12T09:00:00Z"
}

* **Error Responses:**
  * `401 Unauthorized`: Invalid or missing Firebase ID token.
  * `404 Not Found`: User profile not found.

### **2\. Project Endpoints**

#### **`POST /api/v1/projects`**

* **Description:** Creates a new project from a website URL or an idea.  
* **Access:** Authenticated

**Request Body:**  
{  
  "type": "website", // or "idea"  
  "source": "https://my-ecommerce-store.com" // URL or idea description  
}

*   
* **Success Response (202 \- Accepted):**  
  * The conversion process is asynchronous. The response confirms the job has started.

{  
  "projectId": "proj\_xyz789",  
  "status": "pending",  
  "message": "Project creation initiated."  
}

* 

#### **`GET /api/v1/projects`**

* **Description:** Retrieves a list of all projects for the authenticated user.  
* **Access:** Authenticated

**Success Response (200 \- OK):**  
\[  
  {  
    "projectId": "proj\_xyz789",  
    "appName": "My E-commerce App",  
    "status": "completed",  
    "createdAt": "2025-07-12T10:00:00Z"  
  }  
\]

* 

#### **`GET /api/v1/projects/:projectId`**

* **Description:** Retrieves the details of a single project.  
* **Access:** Authenticated (User must own the project)

**Success Response (200 \- OK):**  
{  
  "projectId": "proj\_xyz789",  
  "appName": "My E-commerce App",  
  "status": "completed",  
  "customization": {  
    "primaryColor": "\#007bff",  
    "iconUrl": "..."  
  },  
  "builds": {  
    "ios": "path/to/build.ipa",  
    "android": "path/to/build.apk"  
  },  
  "createdAt": "2025-07-12T10:00:00Z"  
}

*   
* **Error Responses:**  
  * `404 Not Found`: Project does not exist.  
  * `403 Forbidden`: User does not have access to this project.

