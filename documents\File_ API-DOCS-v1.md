### **File: `API-DOCS-v1.md`**

### **Description: Defines the API endpoints, request/response formats, and error codes for the Mobilify backend service.**

### **Base URL: `/api`**

### **Authentication:**

All endpoints, unless marked "Public," require an `Authorization` header with a Bearer Token: `Authorization: Bearer <JWT_ACCESS_TOKEN>`

### **1\. Authentication Endpoints**

#### **`POST /auth/signup`**

* **Description:** Creates a new user account.  
* **Access:** Public

**Request Body:**  
{  
  "fullName": "<PERSON> Conner",  
  "email": "<EMAIL>",  
  "password": "a-strong-password"  
}

* 

**Success Response (201 \- Created):**  
{  
  "userId": "user\_abc123",  
  "email": "<EMAIL>",  
  "accessToken": "ey...",  
  "refreshToken": "ey..."  
}

*   
* **Error Responses:**  
  * `400 Bad Request`: Invalid input (e.g., weak password, invalid email).  
  * `409 Conflict`: An account with this email already exists.

#### **`POST /auth/login`**

* **Description:** Authenticates a user and returns new tokens.  
* **Access:** Public

**Request Body:**  
{  
  "email": "<EMAIL>",  
  "password": "a-strong-password"  
}

* 

**Success Response (200 \- OK):**  
{  
  "userId": "user\_abc123",  
  "accessToken": "ey...",  
  "refreshToken": "ey..."  
}

*   
* **Error Responses:**  
  * `401 Unauthorized`: Invalid credentials.

### **2\. Project Endpoints**

#### **`POST /projects`**

* **Description:** Creates a new project from a website URL or an idea.  
* **Access:** Authenticated

**Request Body:**  
{  
  "type": "website", // or "idea"  
  "source": "https://my-ecommerce-store.com" // URL or idea description  
}

*   
* **Success Response (202 \- Accepted):**  
  * The conversion process is asynchronous. The response confirms the job has started.

{  
  "projectId": "proj\_xyz789",  
  "status": "pending",  
  "message": "Project creation initiated."  
}

* 

#### **`GET /projects`**

* **Description:** Retrieves a list of all projects for the authenticated user.  
* **Access:** Authenticated

**Success Response (200 \- OK):**  
\[  
  {  
    "projectId": "proj\_xyz789",  
    "appName": "My E-commerce App",  
    "status": "completed",  
    "createdAt": "2025-07-12T10:00:00Z"  
  }  
\]

* 

#### **`GET /projects/:projectId`**

* **Description:** Retrieves the details of a single project.  
* **Access:** Authenticated (User must own the project)

**Success Response (200 \- OK):**  
{  
  "projectId": "proj\_xyz789",  
  "appName": "My E-commerce App",  
  "status": "completed",  
  "customization": {  
    "primaryColor": "\#007bff",  
    "iconUrl": "..."  
  },  
  "builds": {  
    "ios": "path/to/build.ipa",  
    "android": "path/to/build.apk"  
  },  
  "createdAt": "2025-07-12T10:00:00Z"  
}

*   
* **Error Responses:**  
  * `404 Not Found`: Project does not exist.  
  * `403 Forbidden`: User does not have access to this project.

