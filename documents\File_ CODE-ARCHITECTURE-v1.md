### **File: `CODE-ARCHITECTURE-v1.md`**

### **Description: Defines the intended file and directory structure for the Mobilify frontend and backend projects.**

### **1\. Frontend Architecture (Marketing Site & Dashboard)**

The frontend will be organized to clearly separate static marketing pages from the dynamic, authenticated dashboard application.

/mobilify-frontend/  
|  
├── /public/                  \# Static assets (images, favicon)  
|   ├── /images/  
|   └── favicon.ico  
|  
├── /src/  
|   ├── /components/          \# Shared React components (Button, Input, etc.)  
|   |   ├── /ui/              \# General-purpose UI components  
|   |   └── /layout/          \# Layout components (Header, Footer, Sidebar)  
|   |  
|   ├── /pages/               \# Next.js page routes  
|   |   ├── /api/             \# API routes (if using Next.js API routes for BFF)  
|   |   ├── /dashboard/       \# Dashboard pages (protected routes)  
|   |   |   └── index.js      \# Main dashboard view  
|   |   ├── \_app.js           \# Global App component  
|   |   ├── index.js          \# Homepage  
|   |   └── pricing.js        \# Pricing Page  
|   |  
|   ├── /lib/                 \# Helper functions, hooks, API clients  
|   |   └── api.js            \# Centralized API request functions  
|   |  
|   └── /styles/              \# Global styles  
|       └── globals.css       \# Tailwind CSS base styles  
|  
├── package.json  
└── tailwind.config.js

*This structure assumes a Next.js setup, which is ideal for this project. For the initial static HTML files, we will place them in the root or a `/dist` folder, but this structure is the end-goal.*

### **2\. Backend Architecture (Node.js/Express API)**

The backend will follow a modular, service-oriented structure to keep concerns separated.

/mobilify-backend/  
|  
├── /src/  
|   ├── /api/                 \# API routes and controllers  
|   |   ├── users.routes.js  
|   |   ├── auth.routes.js  
|   |   └── projects.routes.js  
|   |  
|   ├── /config/              \# Configuration files (db, environment vars)  
|   |   └── index.js  
|   |  
|   ├── /services/            \# Business logic (e.g., user creation, project processing)  
|   |   ├── auth.service.js  
|   |   └── project.service.js  
|   |  
|   ├── /middleware/          \# Express middleware (e.g., auth checks, error handling)  
|   |   └── auth.middleware.js  
|   |  
|   ├── /models/              \# Data models (if using an ORM/ODM)  
|   |   └── user.model.js  
|   |  
|   └── app.js                \# Main Express application setup  
|   └── server.js             \# Server entry point  
|  
└── package.json

