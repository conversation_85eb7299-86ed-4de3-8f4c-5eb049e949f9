### **File: `CODE-ARCHITECTURE-v1.md`**

### **Description: Defines the intended file and directory structure for the Mobilify frontend and backend projects.**

### **1\. Frontend Architecture (Next.js Application)**

**DEFINITIVE APPROACH:** We will start directly with Next.js, building both static marketing pages and dynamic dashboard within a single application.

/mobilify-frontend/
|
├── /public/                  \# Static assets (images, favicon, icons)
|   ├── /images/
|   ├── /icons/               \# Heroicons and custom icons
|   └── favicon.ico
|
├── /src/
|   ├── /components/          \# Shared React components
|   |   ├── /ui/              \# General-purpose UI components (Button, Input, Card)
|   |   ├── /layout/          \# Layout components (Header, Footer, Sidebar)
|   |   ├── /forms/           \# Form components (SignupForm, LoginForm)
|   |   └── /dashboard/       \# Dashboard-specific components
|   |
|   ├── /pages/               \# Next.js page routes
|   |   ├── /dashboard/       \# Protected dashboard pages
|   |   |   └── index.js      \# Main dashboard view
|   |   ├── /auth/            \# Authentication pages
|   |   |   ├── signup.js     \# Sign-up page
|   |   |   └── login.js      \# Login page
|   |   ├── \_app.js           \# Global App component with Firebase Auth provider
|   |   ├── index.js          \# Homepage
|   |   └── pricing.js        \# Pricing page
|   |
|   ├── /lib/                 \# Helper functions, hooks, API clients
|   |   ├── firebase.js       \# Firebase configuration and initialization
|   |   ├── api.js            \# API request functions
|   |   └── auth.js           \# Authentication utilities and hooks
|   |
|   ├── /hooks/               \# Custom React hooks
|   |   └── useAuth.js        \# Authentication state management
|   |
|   └── /styles/              \# Global styles
|       └── globals.css       \# Tailwind CSS base styles
|
├── /tests/                   \# Test files
|   ├── /components/          \# Component tests
|   ├── /pages/               \# Page tests
|   └── /utils/               \# Utility function tests
|
├── package.json
├── tailwind.config.js
├── jest.config.js            \# Jest testing configuration
├── .eslintrc.json            \# ESLint configuration
└── .prettierrc               \# Prettier configuration

### **2\. Backend Architecture (Node.js/Express API)**

The backend will follow a modular, service-oriented structure to keep concerns separated.

/mobilify-backend/  
|  
├── /src/  
|   ├── /api/                 \# API routes and controllers  
|   |   ├── users.routes.js  
|   |   ├── auth.routes.js  
|   |   └── projects.routes.js  
|   |  
|   ├── /config/              \# Configuration files (db, environment vars)  
|   |   └── index.js  
|   |  
|   ├── /services/            \# Business logic (e.g., user creation, project processing)  
|   |   ├── auth.service.js  
|   |   └── project.service.js  
|   |  
|   ├── /middleware/          \# Express middleware (e.g., auth checks, error handling)  
|   |   └── auth.middleware.js  
|   |  
|   ├── /models/              \# Data models (if using an ORM/ODM)  
|   |   └── user.model.js  
|   |  
|   └── app.js                \# Main Express application setup  
|   └── server.js             \# Server entry point  
|  
└── package.json

