### **File: `CODING-STANDARDS-v1.md`**

### **Description: Provides rules and conventions for writing clean, consistent, and maintainable code.**

### **1\. General**

* **Language:** All code and comments must be in English.  
* **Formatting:** Use a code formatter like **Prettier** with default settings to ensure consistent style.  
* **Comments:**  
  * Use `//` for single-line comments.  
  * Use `/** ... */` for JSDoc-style comments for functions and modules. Explain *why* the code exists, not just *what* it does.

### **2\. JavaScript / Node.js**

* **Naming Conventions:**  
  * Variables and functions: `camelCase`  
  * Classes and Components: `PascalCase`  
  * Constants: `UPPER_SNAKE_CASE`  
* **Variables:** Use `const` by default. Use `let` only when a variable needs to be reassigned. Avoid `var`.  
* **Modules:** Use ES6 modules (`import`/`export`).

### **3\. HTML**

* **Semantics:** Use semantic HTML5 tags (`<header>`, `<main>`, `<section>`, `<nav>`, `<footer>`) wherever possible.  
* **Accessibility:** All images must have `alt` attributes. Form inputs should be linked to `<label>`s.

### **4\. Good vs. Bad Example (React Component)**

**Bad:**

// Lacks comments, uses vague naming, inconsistent style  
function MyThing(props) {  
  var d \= props.data;  
  return(\<div\>\<p\>{d.title}\</p\>\</div\>)  
}

**Good:**

/\*\*  
 \* Renders a simple card with a title.  
 \* @param {object} props \- The component props.  
 \* @param {string} props.title \- The title to display in the card.  
 \*/  
function ContentCard({ title }) {  
  return (  
    \<div className="p-4 bg-white rounded-lg shadow"\>  
      \<h3 className="text-lg font-bold"\>{title}\</h3\>  
    \</div\>  
  );  
}

export default ContentCard;

