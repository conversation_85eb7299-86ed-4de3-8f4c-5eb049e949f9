### **File: `DATA-REQS-v1.md`**

### **Description: Defines the data structures, sources, validation rules, and flow for the Mobilify application.**

### **1\. Data Schema & Types**

This section defines the core data entities. The canonical storage format is defined in `DB-SCHEMA-v1.md`. This document focuses on the data in motion.

#### **User Entity**

* **Source:** User Sign-up Form (`signup.html`).  
* **Fields:**  
  * `fullName`: String, non-empty.  
  * `email`: String, must match RFC 5322 standard email format.  
  * `password`: String, will be hashed by the authentication provider and never stored in plaintext.  
* **Transformation:** Upon creation, the backend will generate a `userId` (UUID) and a `createdAt` (Timestamp).

#### **Project Entity**

* **Source:** "Start a New Project" form in the Customer Dashboard.  
* **Fields:**  
  * `userId`: String, foreign key from the authenticated user session.  
  * `sourceType`: String, must be either "website" or "idea".  
  * `sourceValue`: String, non-empty. If `sourceType` is "website", this must be a valid URL format.  
* **Transformation:** The backend will generate a `projectId`, a `status` ("pending"), and `createdAt`/`updatedAt` timestamps.

### **2\. Data Validation Rules**

Validation must be enforced at two levels: client-side for user experience and server-side for security and data integrity.

| Field | Client-Side Validation | Server-Side Validation |
| ----- | ----- | ----- |
| **Email** | `input type="email"`, JS check for `/@/` and `/.` | Regex: `^[^\s@]+@[^\s@]+\.[^\s@]+$`. Check for uniqueness in the `users` collection. |
| **Password** | JS check for minimum length (e.g., 8 characters). | Enforce complexity rules (e.g., length, uppercase, number, special character). |
| **Website URL** | `input type="url"`, JS check for `http://` or `https://`. | Regex to confirm a valid URL structure. Attempt to resolve the domain. |

### **3\. Data Flow Map**

This diagram illustrates how data moves through the system during the primary user flows.

#### **Flow 1: User Sign-up**

\[User @ signup.html\] \--(1. Enters fullName, email, password)--\> \[Frontend JS\]  
         |  
         | (2. Basic validation)  
         V  
\[API Server: POST /api/auth/signup\] \--(3. Secure validation)--\> \[Auth Service\]  
         |  
         | (4. Hashes password, creates user)  
         V  
\[Firestore: writes to /users/{uid}\] \--(5. Returns user object)--\> \[API Server\]  
         |  
         | (6. Generates JWTs)  
         V  
\[User @ signup.html\] \<--(7. Returns user & tokens, redirects to dashboard)-- \[API Server\]

#### **Flow 2: Project Creation**

\[User @ dashboard.html\] \--(1. Enters URL)--\> \[Frontend JS\]  
         |  
         | (2. Sends request with Auth Token)  
         V  
\[API Server: POST /api/projects\] \--(3. Validates token & input)--\> \[Project Service\]  
         |  
         | (4. Creates project document with "pending" status)  
         V  
\[Firestore: writes to /projects/{pid}\] \--(5. Enqueues job)--\> \[Conversion Engine\]  
         |  
         | (6. Returns "Accepted" response)  
         V  
\[User @ dashboard.html\] \<--(7. Displays "Project pending" status)-- \[API Server\]

