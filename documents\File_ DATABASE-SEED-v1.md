### **File: `DATABASE-SEED-v1.md`**

### **Description: Defines sample data for development database seeding to ensure consistent testing environment.**

### **1. Seed Data Overview**

**Purpose:** Provide realistic sample data for development and testing.

**Scope:** 
- 3 sample users with different personas
- 5 sample projects in various states
- Realistic data that matches our target user personas

### **2. Sample Users**

#### **User 1: "The Entrepreneur" (<PERSON>)**
```json
{
  "uid": "dev_user_sarah_001",
  "email": "<EMAIL>",
  "fullName": "<PERSON>",
  "createdAt": "2025-07-10T09:00:00Z",
  "stripeCustomerId": null
}
```

**Firebase Auth Account:**
- Email: <EMAIL>
- Password: DevPassword123!
- Email Verified: true

#### **User 2: "The Small Business Owner" (<PERSON>)**
```json
{
  "uid": "dev_user_david_002", 
  "email": "<EMAIL>",
  "fullName": "<PERSON>",
  "createdAt": "2025-07-08T14:30:00Z",
  "stripeCustomerId": "cus_dev_david_002"
}
```

**Firebase Auth Account:**
- Email: <EMAIL>
- Password: DevPassword123!
- Email Verified: true

#### **User 3: "The Agency Owner" (Maria)**
```json
{
  "uid": "dev_user_maria_003",
  "email": "<EMAIL>", 
  "fullName": "Maria Rodriguez",
  "createdAt": "2025-07-12T11:15:00Z",
  "stripeCustomerId": null
}
```

**Firebase Auth Account:**
- Email: <EMAIL>
- Password: DevPassword123!
- Email Verified: true

### **3. Sample Projects**

#### **Project 1: Completed E-commerce App (David's)**
```json
{
  "projectId": "proj_ecommerce_001",
  "userId": "dev_user_david_002",
  "appName": "Chen's Electronics Store",
  "sourceType": "website",
  "sourceValue": "https://chens-electronics.shopify.com",
  "status": "completed",
  "customization": {
    "primaryColor": "#FF6B35",
    "appIconUrl": "https://storage.googleapis.com/mobilify-dev/icons/chens-icon.png"
  },
  "buildFiles": {
    "ios": "gs://mobilify-builds/proj_ecommerce_001/ios.ipa",
    "android": "gs://mobilify-builds/proj_ecommerce_001/android.apk"
  },
  "createdAt": "2025-07-08T15:00:00Z",
  "updatedAt": "2025-07-09T10:30:00Z"
}
```

#### **Project 2: In Progress Fitness App (Sarah's)**
```json
{
  "projectId": "proj_fitness_002",
  "userId": "dev_user_sarah_001", 
  "appName": "FitTracker Pro",
  "sourceType": "idea",
  "sourceValue": "A fitness tracking app for personal trainers to manage their clients' workout plans and progress",
  "status": "processing",
  "customization": {
    "primaryColor": "#22C55E",
    "appIconUrl": null
  },
  "buildFiles": {
    "ios": null,
    "android": null
  },
  "createdAt": "2025-07-10T10:00:00Z",
  "updatedAt": "2025-07-10T10:00:00Z"
}
```

#### **Project 3: Failed Restaurant App (Maria's)**
```json
{
  "projectId": "proj_restaurant_003",
  "userId": "dev_user_maria_003",
  "appName": "Bella Vista Restaurant",
  "sourceType": "website", 
  "sourceValue": "https://bellavista-restaurant.com",
  "status": "failed",
  "customization": {
    "primaryColor": "#DC2626",
    "appIconUrl": null
  },
  "buildFiles": {
    "ios": null,
    "android": null
  },
  "createdAt": "2025-07-12T12:00:00Z",
  "updatedAt": "2025-07-12T12:45:00Z"
}
```

#### **Project 4: Pending Blog App (Sarah's)**
```json
{
  "projectId": "proj_blog_004",
  "userId": "dev_user_sarah_001",
  "appName": "Tech Insights Blog",
  "sourceType": "website",
  "sourceValue": "https://tech-insights-blog.com", 
  "status": "pending",
  "customization": {
    "primaryColor": "#3B82F6",
    "appIconUrl": null
  },
  "buildFiles": {
    "ios": null,
    "android": null
  },
  "createdAt": "2025-07-12T16:30:00Z",
  "updatedAt": "2025-07-12T16:30:00Z"
}
```

#### **Project 5: Completed Portfolio App (Maria's)**
```json
{
  "projectId": "proj_portfolio_005",
  "userId": "dev_user_maria_003",
  "appName": "Creative Portfolio",
  "sourceType": "website",
  "sourceValue": "https://maria-creative-portfolio.com",
  "status": "completed",
  "customization": {
    "primaryColor": "#8B5CF6",
    "appIconUrl": "https://storage.googleapis.com/mobilify-dev/icons/portfolio-icon.png"
  },
  "buildFiles": {
    "ios": "gs://mobilify-builds/proj_portfolio_005/ios.ipa", 
    "android": "gs://mobilify-builds/proj_portfolio_005/android.apk"
  },
  "createdAt": "2025-07-11T09:15:00Z",
  "updatedAt": "2025-07-11T14:20:00Z"
}
```

### **4. Seed Script Implementation**

#### **Node.js Seed Script Structure**
```javascript
// scripts/seed-database.js
const admin = require('firebase-admin');

const seedUsers = [
  // User data from above
];

const seedProjects = [
  // Project data from above  
];

async function seedDatabase() {
  // Initialize Firebase Admin
  // Create Firebase Auth users
  // Create Firestore documents
  // Log success/failure
}

module.exports = { seedDatabase };
```

#### **Usage Commands**
```bash
# Run seed script
npm run seed

# Clear and reseed database
npm run seed:fresh

# Seed only users
npm run seed:users

# Seed only projects  
npm run seed:projects
```

### **5. Testing Scenarios Enabled**

**With this seed data, developers can test:**

1. **Dashboard with Projects:** David's account shows completed and multiple projects
2. **Empty State:** Maria's account (after clearing her projects) shows empty state
3. **Processing State:** Sarah's fitness app shows in-progress status
4. **Error Handling:** Maria's failed restaurant project tests error states
5. **Different User Types:** Three distinct personas with different use cases

### **6. Seed Data Maintenance**

**Guidelines:**
- Keep seed data realistic and representative
- Update seed data when schema changes
- Include edge cases (failed projects, empty states)
- Maintain consistent timestamps for logical flow
- Use realistic but obviously fake email addresses

**Security Notes:**
- Seed data should only exist in development environment
- Never use real user data for seeding
- Clear seed data before production deployment
- Use environment checks to prevent accidental production seeding
