### **File: `DB-SCHEMA-v1.md`**

### **Description: Defines the data models, relationships, and validation rules for the Firestore database.**

### **Collection: `users`**

Stores information about registered users. The document ID will be the `uid` provided by Firebase Authentication.

**Document ID:** `auth.currentUser.uid`

**Example Document:** `/users/eA5b...zX9p`

{  
  "email": "<EMAIL>",  
  "fullName": "<PERSON>",  
  "createdAt": "2025-07-12T09:00:00Z",  
  "stripeCustomerId": "cus\_abc123" // For future payment integration  
}

| Field | Data Type | Description | Validation Rules |
| ----- | ----- | ----- | ----- |
| `email` | String | User's email address. | Must be a valid email format. Required. |
| `fullName` | String | User's full name. | Required. |
| `createdAt` | Timestamp | Server timestamp of account creation. | Required. |
| `stripeCustomerId` | String | ID from payment processor. | Optional. |

### **Collection: `projects`**

Stores information about each app conversion project initiated by a user.

**Document ID:** Auto-generated by Firestore.

**Example Document:** `/projects/pX8y...aQ4m`

{  
  "userId": "eA5b...zX9p", // Foreign key to the \`users\` collection  
  "appName": "My Awesome Blog App",  
  "sourceType": "website", // "website" or "idea"  
  "sourceValue": "https://my-awesome-blog.com",  
  "status": "completed", // pending, processing, completed, failed  
  "customization": {  
    "primaryColor": "\#3B82F6",  
    "appIconUrl": "https://storage.googleapis.com/..."  
  },  
  "buildFiles": {  
    "ios": "gs://mobilify-builds/pX8y.../ios.ipa",  
    "android": "gs://mobilify-builds/pX8y.../android.apk"  
  },  
  "createdAt": "2025-07-12T10:00:00Z",  
  "updatedAt": "2025-07-12T11:30:00Z"  
}

| Field | Data Type | Description | Validation Rules |
| ----- | ----- | ----- | ----- |
| `userId` | String | The `uid` of the user who owns the project. | Required. Must exist in `users`. |
| `appName` | String | The name of the generated application. | Required. |
| `sourceType` | String | The type of input source. | Must be "website" or "idea". |
| `sourceValue` | String | The URL or idea description. | Required. |
| `status` | String | The current status of the conversion job. | See allowed values in description. |
| `customization` | Map | User-defined app customizations. | Optional. |
| `buildFiles` | Map | Paths to the final build files in cloud storage. | Optional. |
| `createdAt` | Timestamp | Server timestamp of project creation. | Required. |
| `updatedAt` | Timestamp | Server timestamp of the last update. | Required. |

