### **File: `DEPLOYMENT-v1.md`**

### **Description: Defines the deployment strategy, environment configuration, and CI/CD pipeline for the Mobilify project.**

### **1. Environment Configuration**

#### **Required Environment Variables**

**Environment-Specific Configuration:**

**Development (.env):**
```
# Firebase Configuration
FIREBASE_PROJECT_ID=mobilify-dev
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Environment
NODE_ENV=development
PORT=8080

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000
```

**Production (.env):**
```
# Firebase Configuration
FIREBASE_PROJECT_ID=mobilify-prod
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Environment
NODE_ENV=production
PORT=8080

# CORS Configuration
ALLOWED_ORIGINS=https://mobilify-app.vercel.app,https://www.mobilify.com
```

**Frontend Environment Variables:**

**Development (.env.local):**
```
# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=mobilify-dev.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=mobilify-dev
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=mobilify-dev.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef123456789012345

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

**Production (.env.local):**
```
# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=mobilify-prod.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=mobilify-prod
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=mobilify-prod.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef123456789012345

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.mobilify.com/api/v1

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### **2. Hosting Strategy**

| Component | Platform | Rationale |
|-----------|----------|-----------|
| **Frontend** | Vercel | Seamless Next.js integration, automatic deployments, global CDN |
| **Backend API** | Google Cloud Run | Serverless, auto-scaling, integrates well with Firebase services |
| **Database** | Firestore | Managed NoSQL database, real-time capabilities |
| **File Storage** | Firebase Storage | Integrated with Firebase ecosystem, secure file handling |

### **3. CI/CD Pipeline (GitHub Actions)**

#### **Workflow File: `.github/workflows/deploy.yml`**

**Triggers:**
- Push to `main` branch (production deployment)
- Pull Request to `main` branch (preview deployment and testing)

**Pipeline Steps:**
1. **Code Checkout**
2. **Node.js Setup** (v18.x LTS)
3. **Install Dependencies** (`npm ci`)
4. **Run Tests** (Jest, ESLint, Prettier check)
5. **Build Application**
6. **Deploy to Staging** (on PR)
7. **Deploy to Production** (on main branch push)

### **4. Branch Protection Rules**

**Main Branch Protection:**
- Require pull request reviews before merging
- Require status checks to pass before merging
- Require branches to be up to date before merging
- Restrict pushes that create files larger than 100MB

**Required Status Checks:**
- `test` (Jest test suite)
- `lint` (ESLint code quality)
- `build` (Successful build verification)

### **5. Domain Configuration**

**Initial Setup:**
- Frontend: `mobilify-app.vercel.app` (Vercel default)
- Backend API: `mobilify-api-xxxxx.run.app` (Cloud Run default)

**Future Custom Domain:**
- Frontend: `www.mobilify.com`
- Backend API: `api.mobilify.com`

### **6. Security Configuration**

**Firebase Security Rules (Firestore):**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own projects
    match /projects/{projectId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

**Firebase Storage Security Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /user-uploads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### **7. Monitoring & Alerts**

**Health Checks:**
- Frontend: Vercel automatic monitoring
- Backend: Cloud Run health check endpoint (`/health`)

**Error Tracking:**
- Use Google Cloud Error Reporting for backend errors
- Use Vercel Analytics for frontend performance monitoring

**Alerts:**
- API response time > 500ms
- Error rate > 1%
- Uptime < 99.9%
