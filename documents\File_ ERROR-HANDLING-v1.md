### **File: `ERROR-HANDLING-v1.md`**

### **Description: Defines a consistent approach for handling, formatting, and displaying API errors.**

### **0\. Client-Side Error Display Strategy**

**DEFINITIVE APPROACH:** Hybrid error display system:

**Inline Form Errors:**
- Used for validation errors on form fields
- Display directly below the input field
- Red border on invalid input + red text message
- Examples: "A valid email is required", "Password must be at least 8 characters"

**Toast Notifications:**
- Used for general API/server errors
- Appear as temporary banners at top of screen
- Auto-dismiss after 5 seconds or manual close
- Examples: "Failed to save project", "Network error, please try again"

**Error Logging:**
- v1.0: Server-side logging only (no client-side error tracking service)
- Use console.error() for development debugging

### **1\. Standard Error Response Format**

All API errors will return a JSON object with the following structure:

{  
  "error": {  
    "status": 400,  
    "message": "A human-readable description of the error.",  
    "code": "INVALID\_INPUT" // A machine-readable error code  
  }  
}

### **2\. Common HTTP Status Codes**

| Code | Status | Meaning |
| ----- | ----- | ----- |
| **400** | Bad Request | The server cannot or will not process the request due to a client error (e.g., malformed request syntax, invalid input). |
| **401** | Unauthorized | The client must authenticate itself to get the requested response. Used when no or invalid authentication credentials are provided. |
| **403** | Forbidden | The client does not have access rights to the content. Unlike 401, the client's identity is known to the server. |
| **404** | Not Found | The server cannot find the requested resource. |
| **409** | Conflict | The request conflicts with the current state of the server (e.g., creating a user that already exists). |
| **500** | Internal Server Error | The server has encountered a situation it doesn't know how to handle. This is a generic "catch-all" error. |

