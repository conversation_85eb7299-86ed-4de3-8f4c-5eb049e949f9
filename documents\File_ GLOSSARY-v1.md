### **File: `GLOSSARY-v1.md`**

### **Description: Defines project-specific terms and acronyms to ensure clear communication.**

| Term | Definition |
| ----- | ----- |
| **Mobilify** | The name of the service and project. |
| **Website-to-App** | The core process of converting an existing website into a mobile application. |
| **Idea-to-App** | The process of generating an app from a user's description and answers to a questionnaire, without a pre-existing website. |
| **Conversion Engine** | The backend system (likely AI/ML-powered) responsible for analyzing the source and generating the mobile app code. |
| **Customer Dashboard** | The secure, authenticated web interface where users manage their projects, customize their apps, and download builds. |
| **PRD** | Product Requirements Document. The strategic "why" of the project. |
| **PRP** | Product Requirements Prompts. The tactical, AI-taskable "how" of the project. |
| **NFR** | Non-Functional Requirements. System qualities like performance, security, and uptime. |

