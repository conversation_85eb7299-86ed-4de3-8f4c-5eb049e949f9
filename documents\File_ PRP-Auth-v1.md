### **File: `PRP-Auth-v1.md`**

### **Description: A set of AI-taskable prompts to code the user sign-up and login pages.**

### **Prompt 1 of 3: Create the Sign-Up Page**

**Tone:** Direct, structured

**Prompt:** "Now we need a way for users to create an account.

Please create a new HTML file named `signup.html`. It should have a very clean and minimal layout. No main header or footer is needed on this page, just a simple "Mobilify" text logo at the top.

Center a form container on the page. The container should have a headline (h1) "Create Your Account" and the following form fields:

* `input` for "Full Name"  
* `input` for "Email Address" (type="email")  
* `input` for "Password" (type="password")

Below the fields, add a full-width, primary "Create Account" button. Finally, add a text link below the button: "Already have an account? Log in" which should link to `login.html`.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 2 of 3: Create the Login Page**

**Tone:** Direct, structured

**Prompt:** "Next, let's create the login page.

Create a new file `login.html` with the same minimal layout as the sign-up page (Mobilify logo at the top, centered form).

The form container should have the headline (h1) "Welcome Back" and the following fields:

* `input` for "Email Address" (type="email")  
* `input` for "Password" (type="password")

Add a full-width, primary "Log In" button. Below that, include two text links:

1. "Forgot your password?" (linking to `#` for now)  
2. "Don't have an account? Sign up" (linking to `signup.html`)

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 3 of 3: Add Basic Client-Side Validation**

**Tone:** Technical, specific

**Prompt:** "Let's add a better user experience to our forms.

Using JavaScript, please add simple client-side validation to both `signup.html` and `login.html`.

The script should perform the following checks when the user clicks the submit button:

1. Ensure no fields are empty.  
2. For the email field, check that the input is a valid email format.

If a field is invalid, prevent the form from submitting and add a red border to the invalid input field. You can also display a simple error message below the field, like "Please enter a valid email."

**Input/Output Example:**

* **Input:** User leaves the email field empty on `login.html` and clicks "Log In".  
* **Output:** The form does not submit. The email input field gets a red border, and a small red text message appears below it.

**Tech Stack:** `HTML`, `Tailwind CSS`, `JavaScript`"

