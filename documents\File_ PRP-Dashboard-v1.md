### **File: `PRP-Dashboard-v1.md`**

### **Description: A set of AI-taskable prompts to code the basic customer dashboard layout.**

### **Prompt 1 of 3: Create the Dashboard Layout**

**Tone:** Architectural, clear

**Prompt:** "It's time to build the logged-in experience. Let's create the main dashboard layout.

Create a new file `dashboard.html`. The layout should be a two-column design:

1. **A fixed sidebar on the left:** This should have a dark background (`#111827`), similar to the site's header/footer. It will be our main dashboard navigation.  
2. **A main content area on the right:** This should have a light gray background (`#f3f4f6`) where the page content will be displayed.

At the top of the sidebar, place the "Mobilify" logo.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 2 of 3: Build the Sidebar Navigation**

**Tone:** Specific, instructional

**Prompt:** "Let's populate the sidebar we just created.

Inside the dark sidebar, please add a vertical list of navigation links. Each link should have a small SVG icon next to the text.

**Navigation Items:**

* **My Projects:** (Should look like the active link)  
* **Account Settings:**  
* **Support:**

At the very bottom of the sidebar, add a "Logout" link, also with an icon. Style the links with a white or light gray text color so they are readable.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 3 of 3: Design the "Empty State" for the Main Content Area**

**Tone:** Encouraging, user-focused

**Prompt:** "Let's design the initial view for a new user in the main content area of the dashboard. This is the 'empty state' when they have no projects.

The content should be centered and include:

1. A large, friendly headline (h1): "Welcome to Mobilify\!"  
2. A paragraph of text: "You don't have any active projects yet. Let's change that\! Start by converting your website or brainstorming a new idea."  
3. A large, primary call-to-action button: "+ Start a New Project"

This creates a clear next step for the user, guiding them directly into our core service funnel.

**Feedback Loop:** Please show me the final `dashboard.html` file. I will check to ensure the layout is responsive and the empty state is clear and encouraging.

**Tech Stack:** `HTML`, `Tailwind CSS`"

