### **File: `PRP-Homepage-v1.md`**

### **Description: A set of AI-taskable prompts to code the Mobilify marketing homepage.**

### **Prompt 1 of 6: Create the Basic Page Layout and Header**

**Tone:** Friendly, encouraging

**Prompt:** "Hey there\! Let's start building the Mobilify homepage.

First, please create a new HTML file. Set up the basic structure with the `<!DOCTYPE html>`, `<html>`, `<head>`, and `<body>` tags.

Inside the `<head>`, please include:

1. The title: "Mobilify | Convert Your Website or Idea into a Mobile App"  
2. Meta tags for viewport and character encoding.  
3. A link to Tailwind CSS for styling.  
4. A link to the "Inter" font from Google Fonts.

Next, create a responsive header section. It should have a clean, modern design with a dark background (`#111827`). The header needs to contain:

* On the left, a simple text logo that says "**Mobilify**" in a bold, white font.  
* On the right, a navigation menu with the links: "How It Works," "Features," "Pricing," and a standout "Get Started" button with a blue gradient background.

Make sure the header is fully responsive and looks great on mobile, with the navigation links collapsing into a hamburger menu.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 2 of 6: Build the Hero Section**

**Tone:** Collaborative, clear

**Prompt:** "Great job on the header\! Now, let's create the main hero section right below it. This is the first thing users will see.

The section should have a subtle gradient background from a light gray to white. It needs to contain:

1. **Main Headline (h1):** "Turn Your Website or Idea Into a Stunning Mobile App"  
2. **Sub-headline (p):** "No code, no complexity. Just your vision, brought to life. Mobilify uses AI to design, build, and prepare your mobile app for launch in minutes."  
3. **Call to Action:** An input field for the user to paste their website URL, with placeholder text "Enter your website URL...". Next to it, a primary button that says "Start Conversion".

Please center all the content and ensure there's plenty of vertical padding to make it feel open and modern.

**Input/Output Example:**

* **Input:** User sees the hero section.  
* **Output:** User can type a URL into the input field and click the "Start Conversion" button. (The button doesn't need to be functional yet).

**Edge Cases:**

* Ensure the input field and button stack nicely on smaller mobile screens.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 3 of 6: Design the "How It Works" Section**

**Tone:** Instructional, simple

**Prompt:** "Perfect. Now, let's explain the process to our users. Please create a "How It Works" section below the hero.

This section should have a clean, white background and a title like "Get Your App in 3 Simple Steps".

Below the title, create a responsive three-column layout. Each column represents a step and should contain:

1. A simple, modern icon (you can use SVG placeholders for now).  
2. A step title (e.g., "Step 1: Submit").  
3. A short description.

**Content for the steps:**

* **Step 1:** Title: "Submit Your Vision". Description: "Enter your website URL or answer a few simple questions about your app idea."  
* **Step 2:** Title: "AI-Powered Design". Description: "Our AI analyzes your content and brand to generate a beautiful, functional app design."  
* **Step 3:** Title: "Customize & Launch". Description: "Tweak the design, add features in your dashboard, and get your files ready for the app stores."

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 4 of 6: Create the Features Section**

**Tone:** Informative, direct

**Prompt:** "Time to showcase the value. Let's build a "Features" section.

This section should have a slightly off-white background (`#f9fafb`) to separate it from the previous one. Start with a centered headline: "Everything You Need to Succeed".

Below the headline, create a responsive grid (2 columns on desktop, 1 on mobile). Each item in the grid should be a "feature card" with a small icon, a feature title, and a brief description.

**Features to include:**

* **AI-Powered Conversion:** We intelligently transform your existing web content into native app components.  
* **Full Customization:** Easily change colors, fonts, layouts, and your app's icon from your dashboard.  
* **Push Notifications:** Engage your users and drive them back to your app with targeted push notifications.  
* **iOS & Android:** Get ready-to-publish builds for both the Apple App Store and Google Play Store.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 5 of 6: Add a Final Call-to-Action (CTA)**

**Tone:** Energetic, action-oriented

**Prompt:** "Let's add one final push to get users started. Create a simple, bold Call-to-Action section.

It should have a visually appealing background, maybe using the same blue gradient as the header button.

Center the text, which should include:

* A compelling headline: "Ready to Mobilify Your Business?"  
* A primary "Get Started For Free" button.

This section should be clean and focused entirely on getting the user to click that button.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 6 of 6: Build the Footer**

**Tone:** Standard, professional

**Prompt:** "Last step for the homepage\! Please create a footer.

Use the same dark background as the header (`#111827`) for consistency. The footer should be simple and include:

* A copyright notice: "© 2025 Mobilify. All Rights Reserved."  
* A few links, such as "Terms of Service" and "Privacy Policy." (These links can just be `#` for now).  
* Simple social media icons (e.g., Twitter, LinkedIn) on the right side.

Ensure the text is a light gray color for readability against the dark background.

**Feedback Loop:** Once you're done, please show me the complete homepage. I'll review it and provide feedback on any small adjustments needed.

**Tech Stack:** `HTML`, `Tailwind CSS`"

