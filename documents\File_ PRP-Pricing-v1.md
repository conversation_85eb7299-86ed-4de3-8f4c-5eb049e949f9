### **File: `PRP-Pricing-v1.md`**

### **Description: A set of AI-taskable prompts to code the Mobilify pricing page.**

### **Prompt 1 of 4: Create the Basic Page and Header**

**Tone:** Friendly, direct

**Prompt:** "Hello again\! Let's build the pricing page.

Please create a new HTML file named `pricing.html`. Set up the basic structure and link to Tailwind CSS and the 'Inter' font, just like we did for the homepage.

Then, reuse the exact same responsive header component from the homepage. The navigation links should be "How It Works," "Features," and "Pricing," with the "Pricing" link styled as the active page.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 2 of 4: Build the Pricing Section**

**Tone:** Collaborative, detailed

**Prompt:** "Time for the most important part. Below the header, let's add the pricing section.

Start with a centered headline (h1): "Choose the Plan That's Right For You" and a sub-headline (p): "Simple, transparent pricing. No hidden fees."

Next, create a responsive layout that will hold our pricing cards (it should stack to a single column on mobile). Please create two pricing cards:

**Card 1: Starter**

* **Price:** "$299"  
* **Perk:** "One-Time Payment"  
* **Description:** "Perfect for getting your MVP to market quickly."  
* **Features List:**  
  * Website-to-App Conversion  
  * iOS & Android Builds  
  * AI-Powered Design  
  * Standard Customization  
  * Community Support  
* **Button:** A primary "Choose Starter" button.

**Card 2: Pro (with a "Most Popular" badge)**

* **Price:** "$599"  
* **Perk:** "One-Time Payment"  
* **Description:** "For businesses ready to scale and engage their audience."  
* **Features List:**  
  * Everything in Starter  
  * **Push Notifications**  
  * **Idea-to-App Questionnaire**  
  * Advanced Customization  
  * Priority Email Support  
* **Button:** A visually distinct, primary "Choose Pro" button.

Make the "Pro" card stand out with a slightly larger size or a different border color.

**Tech Stack:** `HTML`, `Tailwind CSS`"

### **Prompt 3 of 4: Add an FAQ Section**

**Tone:** Helpful, clear

**Prompt:** "Great. Users always have questions about pricing. Let's add a simple FAQ section below the pricing cards.

The section should have a headline like "Frequently Asked Questions".

Below the headline, create a simple, clean list of questions and answers. An accordion-style dropdown for each question would be ideal for a clean look, but a simple list is fine for now.

**FAQ Content:**

* **Q: Is this really a one-time payment?**  
  * A: Yes\! You pay once to have your app generated and delivered. The only recurring costs are your own developer accounts with Apple and Google.  
* **Q: What if I need changes after my app is built?**  
  * A: Minor updates and bug fixes are covered. For major new features, we offer separate development packages.  
* **Q: Do you publish the app for me?**  
  * A: We provide you with the final build files and a step-by-step guide to publishing on the App Store and Google Play Store.

**Tech Stack:** `HTML`, `Tailwind CSS`, `JavaScript` (for accordion if implemented)"

### **Prompt 4 of 4: Add the Footer**

**Tone:** Standard, professional

**Prompt:** "To finish the pricing page, please add the exact same footer component that we created for the homepage. It should be consistent across the entire site.

**Feedback Loop:** Once you're done, please show me the complete `pricing.html` page. I'll review it for consistency and clarity.

**Tech Stack:** `HTML`, `Tailwind CSS`"

