### **File: `SECURITY-CONFIG-v1.md`**

### **Description: Defines security configurations, rate limiting, CORS policies, and Firebase Auth customization.**

### **1. CORS Configuration**

**DEFINITIVE POLICY:** Restrict CORS to specific domains only.

**Development Environment:**
```javascript
const corsOptions = {
  origin: ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
```

**Production Environment:**
```javascript
const corsOptions = {
  origin: [
    'https://mobilify-app.vercel.app',
    'https://www.mobilify.com'
  ],
  credentials: true,
  optionsSuccessStatus: 200
};
```

### **2. Rate Limiting Configuration**

**Implementation:** Use `express-rate-limit` middleware.

**Authentication Endpoints:**
```javascript
// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: {
    error: {
      status: 429,
      message: "Too many authentication attempts, please try again later.",
      code: "RATE_LIMIT_EXCEEDED"
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply to auth routes
app.use('/api/v1/auth', authLimiter);
```

**General API Endpoints:**
```javascript
// General rate limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: {
      status: 429,
      message: "Too many requests, please try again later.",
      code: "RATE_LIMIT_EXCEEDED"
    }
  }
});

// Apply to all API routes
app.use('/api/v1', generalLimiter);
```

**Project Creation Endpoint:**
```javascript
// Stricter limiting for resource-intensive operations
const projectLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit each IP to 10 project creations per hour
  message: {
    error: {
      status: 429,
      message: "Project creation limit reached. Please try again later.",
      code: "PROJECT_LIMIT_EXCEEDED"
    }
  }
});

app.use('/api/v1/projects', projectLimiter);
```

### **3. Firebase Auth Email Template Customization**

**Required Customizations:**

**Password Reset Email:**
- Subject: "Reset your Mobilify password"
- Sender Name: "Mobilify Team"
- Custom HTML template with Mobilify branding
- Include company logo and consistent styling

**Email Verification:**
- Subject: "Verify your Mobilify account"
- Sender Name: "Mobilify Team"
- Welcome message with next steps
- Clear call-to-action button

**Configuration Steps:**
1. Go to Firebase Console → Authentication → Templates
2. Customize each template with:
   - Mobilify logo
   - Brand colors (#3B82F6 primary, #111827 dark)
   - Professional email signature
   - Support contact information

### **4. Input Validation & Sanitization**

**Server-Side Validation Rules:**

**Email Validation:**
```javascript
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const isValidEmail = (email) => emailRegex.test(email) && email.length <= 254;
```

**URL Validation (for website-to-app):**
```javascript
const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const isValidUrl = (url) => urlRegex.test(url) && url.length <= 2048;
```

**Input Sanitization:**
- Use `express-validator` for request validation
- Sanitize all string inputs to prevent XSS
- Validate all required fields before processing

### **5. Firebase Security Rules**

**Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == userId &&
        validateUserData();
    }
    
    // Users can only access their own projects
    match /projects/{projectId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid &&
        validateProjectData();
    }
    
    // Helper functions
    function validateUserData() {
      return request.resource.data.keys().hasAll(['email', 'fullName', 'createdAt']) &&
        request.resource.data.email is string &&
        request.resource.data.fullName is string;
    }
    
    function validateProjectData() {
      return request.resource.data.keys().hasAll(['userId', 'appName', 'sourceType', 'sourceValue']) &&
        request.resource.data.sourceType in ['website', 'idea'] &&
        request.resource.data.userId == request.auth.uid;
    }
  }
}
```

**Firebase Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User uploads (app icons, etc.)
    match /user-uploads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.uid == userId &&
        resource.size < 5 * 1024 * 1024 && // 5MB limit
        resource.contentType.matches('image/.*');
    }
    
    // Generated app builds (read-only for users)
    match /builds/{userId}/{allPaths=**} {
      allow read: if request.auth != null && 
        request.auth.uid == userId;
      allow write: if false; // Only server can write builds
    }
  }
}
```

### **6. Security Headers**

**Helmet.js Configuration:**
```javascript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.mobilify.com"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### **7. Environment-Specific Security**

**Development:**
- Relaxed CORS for localhost
- Detailed error messages for debugging
- Console logging enabled

**Production:**
- Strict CORS policy
- Generic error messages for security
- Structured logging to cloud services
- HTTPS enforcement
- Security headers enforcement

### **8. Monitoring & Alerts**

**Security Monitoring:**
- Track failed authentication attempts
- Monitor rate limit violations
- Alert on suspicious activity patterns
- Log all security-related events

**Alert Thresholds:**
- More than 10 failed login attempts from same IP in 5 minutes
- Rate limit exceeded more than 5 times in 1 hour
- Unusual geographic access patterns
- Multiple account creation attempts from same IP
