### **File: `SETUP-GUIDE-v1.md`**

### **Description: Step-by-step guide for setting up the Mobilify development environment and initial project structure.**

### **1. Prerequisites**

**Required Software:**
- Node.js v18.x LTS
- npm (comes with Node.js)
- Git
- Visual Studio Code (recommended)

**Required Accounts:**
- GitHub account
- Google/Firebase account
- Vercel account (for deployment)

### **2. Firebase Project Setup**

#### **Step 1: Create Firebase Project**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Project name: `mobilify-dev` (for initial development)
4. **IMPORTANT:** Enable Google Analytics (required for tracking conversion funnels)
5. Choose or create a Google Analytics account
6. **Note:** Create separate projects later: `mobilify-staging`, `mobilify-prod`

#### **Step 2: Enable Authentication**
1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Configure authorized domains (add your Vercel domain later)

#### **Step 3: Create Firestore Database**
1. Go to "Firestore Database"
2. Click "Create database"
3. Start in "test mode" (we'll add security rules later)
4. Choose a location (us-central1 recommended)

#### **Step 4: Enable Storage**
1. Go to "Storage"
2. Click "Get started"
3. Start in "test mode"
4. Use the same location as Firestore

#### **Step 5: Generate Service Account Key**
1. Go to "Project Settings" (gear icon)
2. Go to "Service accounts" tab
3. Click "Generate new private key"
4. Download the JSON file (keep it secure!)

### **3. Frontend Setup (Next.js)**

#### **Step 1: Initialize Next.js Project**
```bash
npx create-next-app@latest mobilify-frontend
cd mobilify-frontend

# Install additional dependencies
npm install firebase
npm install -D tailwindcss postcss autoprefixer
npm install -D @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
npm install -D eslint-config-prettier prettier
npm install @heroicons/react
```

#### **Step 2: Configure Tailwind CSS**
```bash
npx tailwindcss init -p
```

Update `tailwind.config.js`:
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      colors: {
        primary: '#3B82F6',
        'primary-dark': '#2563EB',
      },
    },
  },
  plugins: [],
}
```

#### **Step 3: Environment Variables**
Create `.env.local`:
```
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=mobilify-dev.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=mobilify-dev
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=mobilify-dev.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### **4. Backend Setup (Node.js/Express)**

#### **Step 1: Initialize Backend Project**
```bash
mkdir mobilify-backend
cd mobilify-backend
npm init -y

# Install dependencies
npm install express cors helmet morgan dotenv
npm install firebase-admin
npm install -D nodemon jest supertest eslint prettier
```

#### **Step 2: Environment Variables**
Create `.env`:
```
FIREBASE_PROJECT_ID=mobilify-dev
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
NODE_ENV=development
PORT=8080
ALLOWED_ORIGINS=http://localhost:3000
```

#### **Step 3: Basic Server Structure**
Create the following directory structure:
```
/src
  /api
    /routes
  /config
  /middleware
  /services
  app.js
  server.js
```

### **5. Development Workflow**

#### **Daily Development Process**
1. **Start Development Servers:**
   ```bash
   # Terminal 1 - Frontend
   cd mobilify-frontend
   npm run dev

   # Terminal 2 - Backend
   cd mobilify-backend
   npm run dev
   ```

2. **Create Feature Branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Development Cycle:**
   - Write code
   - Run tests: `npm test`
   - Check formatting: `npm run lint`
   - Commit changes with descriptive messages

4. **Submit for Review:**
   ```bash
   git push origin feature/your-feature-name
   # Create Pull Request on GitHub
   ```

### **6. Testing Setup**

#### **Frontend Testing (Jest + React Testing Library)**
Create `jest.config.js`:
```javascript
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/pages/(.*)$': '<rootDir>/src/pages/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
}

module.exports = createJestConfig(customJestConfig)
```

#### **Backend Testing (Jest + Supertest)**
Create `jest.config.js` in backend:
```javascript
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
}
```

### **7. Code Quality Setup**

#### **ESLint Configuration (.eslintrc.json)**
```json
{
  "extends": [
    "next/core-web-vitals",
    "prettier"
  ],
  "rules": {
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

#### **Prettier Configuration (.prettierrc)**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### **8. Initial Verification**

After setup, verify everything works:

1. **Frontend:** Visit `http://localhost:3000` - should see Next.js welcome page
2. **Backend:** Visit `http://localhost:8080/health` - should return status
3. **Tests:** Run `npm test` in both projects - should pass
4. **Linting:** Run `npm run lint` - should pass with no errors

### **9. Next Steps**

Once setup is complete, proceed with implementation in this order:
1. Implement basic Next.js pages (homepage, pricing)
2. Set up Firebase Auth integration
3. Create backend API endpoints
4. Build dashboard functionality
5. Add comprehensive testing
6. Set up CI/CD pipeline
