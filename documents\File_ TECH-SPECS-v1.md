### **File: `TECH-SPECS-v1.md`**

### **Description: Defines the system constraints, technology stack, and performance thresholds for the Mobilify project.**

### **1\. Technology Stack**

This section outlines the approved technologies and their specific versions for the project. Adherence to this stack is mandatory for code generation.

| Layer | Technology | Version / Specification | Rationale |
| ----- | ----- | ----- | ----- |
| **Frontend** | HTML | HTML5 | Universal standard for web content. |
| **Styling** | Tailwind CSS | v3.0+ | Utility-first framework for rapid, consistent UI development. |
| **Client-Side JS** | Vanilla JavaScript | ES6+ | For lightweight interactivity on static pages (forms, menus). |
| **JS Framework** | React (Next.js) | React 18+, Next.js 13+ | *To be used for the Customer Dashboard and future dynamic parts of the app.* |
| **Backend API** | Node.js | v18.x LTS | Efficient, scalable, and uses JavaScript, maintaining language consistency. |
| **Backend Framework** | Express.js | v4.x | Minimal and flexible Node.js framework for building the API. |
| **Database** | Firestore | Latest SDK | **DEFINITIVE CHOICE:** Managed, scalable NoSQL database that integrates well with Firebase Auth. |
| **Authentication** | Firebase Auth | Latest SDK | Handles user authentication, session management, and token refresh automatically. |
| **File Storage** | Firebase Storage | Latest SDK | For user-uploaded files (app icons, etc.). |
| **Package Manager** | npm | Latest LTS | Default Node.js package manager for consistent dependency management. |
| **Deployment** | Vercel | \- | For frontend hosting, offering seamless CI/CD and CDN. |
| **Backend Hosting** | Google Cloud Run | \- | Serverless platform for containerized backend applications. |
| **CI/CD** | GitHub Actions | \- | Automated testing and deployment pipeline. |

### **2\. Architecture**

The system will follow a standard client-server architecture.

* **Client (Frontend):** A Next.js application serving both static marketing pages and dynamic dashboard. Uses Firebase Auth SDK for client-side authentication and communicates with the backend via RESTful API.
* **Server (Backend):** A Node.js/Express application that handles business logic and database interactions. Uses Firebase Admin SDK for server-side authentication verification.
* **Database:** Firestore stores all persistent data (user accounts, project details). Firebase Storage handles file uploads.

\+------------------------+      \+-------------------------+  
|   Browser (Client)     |      |   Mobile App (Future)   |  
\+------------------------+      \+-------------------------+  
           |                                |  
           \+--------------+-----------------+  
                          |  
                          | HTTPS (REST API)  
                          V  
\+---------------------------------------------------------+  
|          Backend: Node.js / Express on Cloud Run        |  
|                                                         |  
|  \+-----------------+  \+-----------------+  \+----------+ |  
|  |  Auth Service   |  | Project Service |  |   API    | |  
|  \+-----------------+  \+-----------------+  \+----------+ |  
\+---------------------------------------------------------+  
                          |  
                          | Firestore SDK  
                          V  
\+---------------------------------------------------------+  
|                  Database (Firestore)                   |  
\+---------------------------------------------------------+

### **3\. Performance & Security Thresholds**

* **API Response Time:** All standard API endpoints must respond in **\< 250ms** under normal load.  
* **Website Load Time:** The marketing homepage must achieve a Largest Contentful Paint (LCP) of **\< 2.5 seconds**.  
* **Security:**
  * Authentication will be handled by Firebase Auth SDK (client-side) and Firebase Admin SDK (server-side verification).
  * All API endpoints, except for public informational ones, must be protected and require a valid Firebase ID token.
  * Input validation will be enforced on both the client-side (as a UX enhancement) and server-side (for security).

### **4\. API Contract Philosophy**

* The API will be designed using a **RESTful** approach.  
* Request and response bodies will use **JSON**.  
* Endpoint naming will be plural and resource-oriented (e.g., `/api/users`, `/api/projects`).  
* Standard HTTP verbs will be used for actions: `GET` (retrieve), `POST` (create), `PUT`/`PATCH` (update), `DELETE` (remove).

