### **File: `UI-UX-GUIDE-v1.md`**

### **Description: Provides visual and interactive guidance for all front-end development, ensuring a consistent and professional user experience.**

### **1\. Color Palette**

| Role | Color Name | HEX Code | Tailwind Class | Usage |
| ----- | ----- | ----- | ----- | ----- |
| **Primary** | Brand Blue | `#3B82F6` | `bg-blue-500` | Buttons, links, active states, highlights. |
| **Accent** | Gradient Start | `#2563EB` | `from-blue-600` | Used in gradients for standout CTAs. |
| **Accent** | Gradient End | `#3B82F6` | `to-blue-500` | Used in gradients for standout CTAs. |
| **Dark** | Gray 900 | `#111827` | `bg-gray-900` | Header, footer, sidebars. |
| **Neutral** | Gray 700 | `#374151` | `text-gray-700` | Body text, headlines. |
| **Neutral** | Gray 500 | `#6B728D` | `text-gray-500` | Sub-headings, helper text. |
| **Background** | Gray 50 | `#F9FAFB` | `bg-gray-50` | Section backgrounds for subtle contrast. |
| **White** | White | `#FFFFFF` | `bg-white` | Main page background, card backgrounds. |
| **Success** | Green 500 | `#22C55E` | `bg-green-500` | Success messages, validation. |
| **Error** | Red 500 | `#EF4444` | `bg-red-500` | Error messages, validation. |

### **2\. Typography**

* **Font Family:** Inter  
* **Body Text:** `text-base` (16px), `font-normal`, `text-gray-700`  
* **H1 (Headline):** `text-4xl` or `text-5xl`, `font-bold`, `text-gray-900`  
* **H2 (Section Title):** `text-3xl`, `font-bold`, `text-gray-900`  
* **H3 (Card Title):** `text-xl`, `font-semibold`, `text-gray-900`

### **3\. Component Library**

#### **Buttons**

* **Primary Button:**  
  * **Style:** `bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-transform transform hover:scale-105`  
  * **Example:** "Get Started"  
* **Secondary Button:**  
  * **Style:** `bg-transparent border-2 border-blue-500 text-blue-500 hover:bg-blue-50 font-bold py-3 px-6 rounded-lg transition`  
  * **Example:** "Learn More"

#### **Form Inputs**

* **Default State:** `block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm`  
* **Error State:** Add `border-red-500 ring-red-500` classes.

### **4\. Spacing & Layout**

* **Standard Padding:** Use multiples of 4\. A standard padding for cards and containers is `p-6` or `p-8`.  
* **Section Spacing:** Vertical spacing between major homepage sections should be generous, e.g., `py-16` or `py-20`.  
* **Corners:** All elements (buttons, cards, inputs) should have rounded corners, typically `rounded-lg`.

