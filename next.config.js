/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['storage.googleapis.com'],
  },
  env: {
    CUSTOM_KEY: 'my-value',
  },
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
  // Exclude test files from being treated as pages
  webpack: (config) => {
    config.module.rules.push({
      test: /\.test\.(js|jsx|ts|tsx)$/,
      loader: 'ignore-loader',
    });
    return config;
  },
}

module.exports = nextConfig
