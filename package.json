{"name": "mobilify-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@heroicons/react": "^2.0.18", "firebase": "^12.0.0", "next": "^14.2.30", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}