import React from 'react';
import { useRouter } from 'next/router';
import { 
  FolderIcon, 
  Cog6ToothIcon, 
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

/**
 * Dashboard layout component with sidebar navigation
 * Implements PRP-Dashboard-v1.md Prompts 1-2: Create the Dashboard Layout and Sidebar Navigation
 */
const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { logout } = useAuth();

  const navigationItems = [
    {
      name: 'My Projects',
      href: '/dashboard',
      icon: FolderIcon,
      current: router.pathname === '/dashboard',
    },
    {
      name: 'Account Settings',
      href: '/dashboard/settings',
      icon: Cog6ToothIcon,
      current: router.pathname === '/dashboard/settings',
    },
    {
      name: 'Support',
      href: '/dashboard/support',
      icon: QuestionMarkCircleIcon,
      current: router.pathname === '/dashboard/support',
    },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col">
        <div className="flex flex-col flex-grow pt-5 bg-gray-900 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4">
            <span className="text-white text-xl font-bold">Mobilify</span>
          </div>

          {/* Navigation */}
          <div className="mt-8 flex-grow flex flex-col">
            <nav className="flex-1 px-2 space-y-1">
              {navigationItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => router.push(item.href)}
                    className={`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left transition-colors
                      ${item.current
                        ? 'bg-gray-800 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      }
                    `}
                  >
                    <IconComponent
                      className={`
                        mr-3 flex-shrink-0 h-5 w-5
                        ${item.current ? 'text-white' : 'text-gray-400 group-hover:text-white'}
                      `}
                    />
                    {item.name}
                  </button>
                );
              })}
            </nav>

            {/* Logout Button */}
            <div className="flex-shrink-0 px-2 pb-4">
              <button
                onClick={handleLogout}
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
              >
                <ArrowRightOnRectangleIcon className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-white" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Mobile menu button - we'll add this later if needed */}
        
        {/* Main content area */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
