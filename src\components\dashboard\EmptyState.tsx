import React from 'react';
import { useRouter } from 'next/router';
import { PlusIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import { trackCTAClick } from '@/lib/analytics';

/**
 * Empty state component for new users with no projects
 * Implements PRP-Dashboard-v1.md Prompt 3/3: Design the "Empty State" for the Main Content Area
 */
const EmptyState: React.FC = () => {
  const router = useRouter();

  const handleStartProject = () => {
    trackCTAClick('dashboard_start_project');
    // For now, redirect to a project creation page (we'll build this later)
    router.push('/dashboard/new-project');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-full py-12 px-4">
      <div className="text-center max-w-md">
        {/* Icon */}
        <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-primary-100 mb-8">
          <PlusIcon className="h-12 w-12 text-primary-500" />
        </div>

        {/* Headline */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Welcome to Mobilify!
        </h1>

        {/* Description */}
        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
          You don&apos;t have any active projects yet. Let&apos;s change that! Start by converting
          your website or brainstorming a new idea.
        </p>

        {/* CTA Button */}
        <Button
          onClick={handleStartProject}
          size="lg"
          className="bg-gradient-primary"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Start a New Project
        </Button>

        {/* Additional help text */}
        <p className="text-sm text-gray-500 mt-6">
          Need help getting started? Check out our{' '}
          <button 
            onClick={() => router.push('/dashboard/support')}
            className="text-primary-500 hover:text-primary-600 underline"
          >
            support resources
          </button>
        </p>
      </div>
    </div>
  );
};

export default EmptyState;
