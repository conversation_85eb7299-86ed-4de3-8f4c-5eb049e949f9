import React from 'react';
import { useRouter } from 'next/router';
import Button from '@/components/ui/Button';
import { trackCTAClick } from '@/lib/analytics';

/**
 * Final Call-to-Action section component for the homepage
 * Implements PRP-Homepage-v1.md Prompt 5/6: Add a Final Call-to-Action (CTA)
 */
const CTASection: React.FC = () => {
  const router = useRouter();

  const handleGetStartedClick = () => {
    trackCTAClick('final_cta_get_started');
    router.push('/auth/signup');
  };

  return (
    <section className="py-20 bg-gradient-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Compelling Headline */}
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
            Ready to Mobilify Your Business?
          </h2>

          {/* Primary CTA Button */}
          <Button
            onClick={handleGetStartedClick}
            size="lg"
            className="bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700 shadow-lg"
          >
            Get Started For Free
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
