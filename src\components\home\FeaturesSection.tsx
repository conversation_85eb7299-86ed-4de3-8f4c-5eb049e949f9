import React from 'react';
import { 
  CpuChipIcon, 
  PaintBrushIcon, 
  BellIcon, 
  DevicePhoneMobileIcon 
} from '@heroicons/react/24/outline';

/**
 * Features section component for the homepage
 * Implements PRP-Homepage-v1.md Prompt 4/6: Create the Features Section
 */
const FeaturesSection: React.FC = () => {
  const features = [
    {
      title: 'AI-Powered Conversion',
      description: 'We intelligently transform your existing web content into native app components.',
      icon: CpuChipIcon,
    },
    {
      title: 'Full Customization',
      description: 'Easily change colors, fonts, layouts, and your app\'s icon from your dashboard.',
      icon: PaintBrushIcon,
    },
    {
      title: 'Push Notifications',
      description: 'Engage your users and drive them back to your app with targeted push notifications.',
      icon: BellIcon,
    },
    {
      title: 'iOS & Android',
      description: 'Get ready-to-publish builds for both the Apple App Store and Google Play Store.',
      icon: DevicePhoneMobileIcon,
    },
  ];

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need to Succeed
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            
            return (
              <div key={index} className="bg-white rounded-lg p-8 shadow-sm hover:shadow-md transition-shadow">
                {/* Feature Icon */}
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <IconComponent className="w-6 h-6 text-primary-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>

                {/* Feature Description */}
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
