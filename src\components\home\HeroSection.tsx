import React, { useState } from 'react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { trackCTAClick } from '@/lib/analytics';

/**
 * Hero section component for the homepage
 * Implements PRP-Homepage-v1.md Prompt 2/6: Build the Hero Section
 */
const HeroSection: React.FC = () => {
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [urlError, setUrlError] = useState('');

  const validateUrl = (url: string): boolean => {
    const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    return urlRegex.test(url);
  };

  const handleStartConversion = () => {
    setUrlError('');
    
    if (!websiteUrl.trim()) {
      setUrlError('Please enter a website URL');
      return;
    }

    if (!validateUrl(websiteUrl)) {
      setUrlError('Please enter a valid website URL (e.g., https://example.com)');
      return;
    }

    // Track the conversion start
    trackCTAClick('hero_start_conversion');
    
    // For now, redirect to signup with the URL as a parameter
    const encodedUrl = encodeURIComponent(websiteUrl);
    window.location.href = `/auth/signup?url=${encodedUrl}`;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleStartConversion();
    }
  };

  return (
    <section className="bg-gradient-to-b from-gray-50 to-white py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Headline */}
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Turn Your Website or Idea Into a Stunning Mobile App
          </h1>

          {/* Sub-headline */}
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
            No code, no complexity. Just your vision, brought to life. Mobilify uses AI to design, 
            build, and prepare your mobile app for launch in minutes.
          </p>

          {/* Call to Action Form */}
          <div className="max-w-2xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 items-start">
              <div className="flex-1 w-full">
                <Input
                  type="url"
                  placeholder="Enter your website URL..."
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                  onKeyPress={handleKeyPress}
                  error={urlError}
                  className="text-lg py-4"
                />
              </div>
              <Button
                onClick={handleStartConversion}
                size="lg"
                className="w-full sm:w-auto whitespace-nowrap bg-gradient-primary"
              >
                Start Conversion
              </Button>
            </div>

            {/* Helper text */}
            <p className="text-sm text-gray-500 mt-4">
              Don't have a website? <span className="text-primary-500 font-medium">Start with an idea instead →</span>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
