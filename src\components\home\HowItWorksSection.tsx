import React from 'react';
import { 
  DocumentTextIcon, 
  CpuChipIcon, 
  RocketLaunchIcon 
} from '@heroicons/react/24/outline';

/**
 * How It Works section component for the homepage
 * Implements PRP-Homepage-v1.md Prompt 3/6: Design the "How It Works" Section
 */
const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      id: 1,
      title: 'Submit Your Vision',
      description: 'Enter your website URL or answer a few simple questions about your app idea.',
      icon: DocumentTextIcon,
    },
    {
      id: 2,
      title: 'AI-Powered Design',
      description: 'Our AI analyzes your content and brand to generate a beautiful, functional app design.',
      icon: CpuChipIcon,
    },
    {
      id: 3,
      title: 'Customize & Launch',
      description: 'Tweak the design, add features in your dashboard, and get your files ready for the app stores.',
      icon: RocketLaunchIcon,
    },
  ];

  return (
    <section id="how-it-works" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Get Your App in 3 Simple Steps
          </h2>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {steps.map((step) => {
            const IconComponent = step.icon;
            
            return (
              <div key={step.id} className="text-center">
                {/* Step Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                </div>

                {/* Step Title */}
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Step {step.id}: {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Connection Lines (Desktop only) */}
        <div className="hidden md:block relative mt-8">
          <div className="absolute top-0 left-1/6 right-1/6 h-0.5 bg-gray-200 transform -translate-y-20"></div>
          <div className="absolute top-0 left-1/2 w-0.5 h-4 bg-gray-200 transform -translate-x-0.5 -translate-y-24"></div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
