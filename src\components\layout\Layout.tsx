import React from 'react';
import Head from 'next/head';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  variant?: 'marketing' | 'dashboard';
}

/**
 * Main layout component that wraps all pages
 * @param children - Page content
 * @param title - Page title for SEO
 * @param description - Page description for SEO
 * @param variant - Layout variant (marketing or dashboard)
 */
const Layout: React.FC<LayoutProps> = ({
  children,
  title = 'Mobilify | Convert Your Website or Idea into a Mobile App',
  description = 'Transform your website or idea into a professional mobile app with AI-powered conversion. No code required.',
  variant = 'marketing',
}) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        
        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:title" content={title} />
        <meta property="twitter:description" content={description} />
      </Head>

      <div className="min-h-screen flex flex-col">
        <Header variant={variant} />
        
        <main className="flex-grow">
          {children}
        </main>
        
        {variant === 'marketing' && <Footer />}
      </div>
    </>
  );
};

export default Layout;
