import React from 'react';
import { useRouter } from 'next/router';
import { CheckIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import { trackCTAClick } from '@/lib/analytics';

/**
 * Pricing cards component for the pricing page
 * Implements PRP-Pricing-v1.md Prompt 2/4: Build the Pricing Section
 */
const PricingCards: React.FC = () => {
  const router = useRouter();

  const handlePlanSelect = (planName: string) => {
    trackCTAClick(`pricing_${planName.toLowerCase()}`);
    router.push(`/auth/signup?plan=${planName.toLowerCase()}`);
  };

  const plans = [
    {
      name: 'Starter',
      price: '$299',
      perk: 'One-Time Payment',
      description: 'Perfect for getting your MVP to market quickly.',
      features: [
        'Website-to-App Conversion',
        'iOS & Android Builds',
        'AI-Powered Design',
        'Standard Customization',
        'Community Support',
      ],
      buttonText: 'Choose Starter',
      popular: false,
    },
    {
      name: 'Pro',
      price: '$599',
      perk: 'One-Time Payment',
      description: 'For businesses ready to scale and engage their audience.',
      features: [
        'Everything in Starter',
        'Push Notifications',
        'Idea-to-App Questionnaire',
        'Advanced Customization',
        'Priority Email Support',
      ],
      buttonText: 'Choose Pro',
      popular: true,
    },
  ];

  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Choose the Plan That&apos;s Right For You
          </h1>
          <p className="text-xl text-gray-600">
            Simple, transparent pricing. No hidden fees.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`
                relative bg-white rounded-lg shadow-lg p-8 border-2 transition-transform hover:scale-105
                ${plan.popular ? 'border-primary-500 transform scale-105' : 'border-gray-200'}
              `}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <div className="mb-2">
                  <span className="text-4xl font-bold text-gray-900">
                    {plan.price}
                  </span>
                </div>
                <p className="text-primary-500 font-medium mb-4">
                  {plan.perk}
                </p>
                <p className="text-gray-600">
                  {plan.description}
                </p>
              </div>

              {/* Features List */}
              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckIcon className="h-5 w-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <Button
                onClick={() => handlePlanSelect(plan.name)}
                className={`
                  w-full
                  ${plan.popular 
                    ? 'bg-gradient-primary' 
                    : 'bg-gray-900 hover:bg-gray-800 text-white'
                  }
                `}
              >
                {plan.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PricingCards;
