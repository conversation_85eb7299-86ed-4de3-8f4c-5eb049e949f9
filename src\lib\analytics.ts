declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: any) => void;
  }
}

export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

// Track page views
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID!, {
      page_path: url,
    });
  }
};

// Track custom events
export const event = (action: string, parameters?: any) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, parameters);
  }
};

// Predefined events for our conversion funnel
export const trackSignup = () => {
  event('sign_up', {
    method: 'email',
  });
};

export const trackLogin = () => {
  event('login', {
    method: 'email',
  });
};

export const trackProjectStart = () => {
  event('project_start');
};

export const trackProjectComplete = () => {
  event('project_complete');
};

export const trackPricingView = () => {
  event('pricing_view');
};

export const trackCTAClick = (cta_name: string) => {
  event('cta_click', {
    cta_name,
  });
};
