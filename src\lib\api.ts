const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

interface ApiError {
  status: number;
  message: string;
  code: string;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'An error occurred');
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Add authorization header for authenticated requests
  private getAuthHeaders(token: string) {
    return {
      Authorization: `Bearer ${token}`,
    };
  }

  // User profile endpoints
  async createUserProfile(token: string, userData: any) {
    return this.request('/users/profile', {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify(userData),
    });
  }

  async getUserProfile(token: string) {
    return this.request('/users/profile', {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
  }

  // Project endpoints
  async createProject(token: string, projectData: any) {
    return this.request('/projects', {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify(projectData),
    });
  }

  async getProjects(token: string) {
    return this.request('/projects', {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
  }

  async getProject(token: string, projectId: string) {
    return this.request(`/projects/${projectId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
  }
}

export const apiClient = new ApiClient(API_BASE_URL || '');
export type { ApiError };
