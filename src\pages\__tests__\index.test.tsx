import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import HomePage from '../index';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock analytics
jest.mock('@/lib/analytics', () => ({
  trackCTAClick: jest.fn(),
}));

const mockPush = jest.fn();
(useRouter as jest.Mock).mockReturnValue({
  push: mockPush,
  pathname: '/',
});

describe('HomePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the main headline', () => {
    render(<HomePage />);
    expect(
      screen.getByText(/Turn Your Website or Idea Into a Stunning Mobile App/i)
    ).toBeInTheDocument();
  });

  it('renders the hero section with URL input', () => {
    render(<HomePage />);
    expect(
      screen.getByPlaceholderText(/Enter your website URL/i)
    ).toBeInTheDocument();
  });

  it('renders the how it works section', () => {
    render(<HomePage />);
    expect(
      screen.getByText(/Get Your App in 3 Simple Steps/i)
    ).toBeInTheDocument();
  });

  it('renders the features section', () => {
    render(<HomePage />);
    expect(
      screen.getByText(/Everything You Need to Succeed/i)
    ).toBeInTheDocument();
  });

  it('renders the final CTA section', () => {
    render(<HomePage />);
    expect(
      screen.getByText(/Ready to Mobilify Your Business/i)
    ).toBeInTheDocument();
  });
});
