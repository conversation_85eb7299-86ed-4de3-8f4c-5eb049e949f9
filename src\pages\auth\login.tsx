import React from 'react';
import Head from 'next/head';
import LoginForm from '@/components/auth/LoginForm';

/**
 * Login page
 * Implements PRP-Auth-v1.md Prompt 2/3
 */
const LoginPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Log In | Mobilify - Access Your Account</title>
        <meta name="description" content="Log in to your Mobilify account to manage your mobile app projects and access your dashboard." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <LoginForm />
    </>
  );
};

export default LoginPage;
