import React from 'react';
import Head from 'next/head';
import SignupForm from '@/components/auth/SignupForm';

/**
 * Sign-up page
 * Implements PRP-Auth-v1.md Prompt 1/3
 */
const SignupPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Sign Up | Mobilify - Create Your Account</title>
        <meta name="description" content="Create your Mobilify account and start building your mobile app today. No code required." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <SignupForm />
    </>
  );
};

export default SignupPage;
