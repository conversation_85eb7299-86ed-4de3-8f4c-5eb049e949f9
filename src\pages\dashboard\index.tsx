import React from 'react';
import Head from 'next/head';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import EmptyState from '@/components/dashboard/EmptyState';

/**
 * Main dashboard page
 * Implements PRP-Dashboard-v1.md prompts 1-3
 */
const DashboardPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Dashboard | Mobilify - Manage Your Mobile App Projects</title>
        <meta name="description" content="Manage your mobile app projects, track progress, and customize your apps from your Mobilify dashboard." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      
      <ProtectedRoute>
        <DashboardLayout>
          <EmptyState />
        </DashboardLayout>
      </ProtectedRoute>
    </>
  );
};

export default DashboardPage;
