import React from 'react';
import Layout from '@/components/layout/Layout';
import HeroSection from '@/components/home/<USER>';
import HowItWorksSection from '@/components/home/<USER>';
import FeaturesSection from '@/components/home/<USER>';
import CTASection from '@/components/home/<USER>';

/**
 * Homepage component
 * Implements all sections from PRP-Homepage-v1.md
 */
const HomePage: React.FC = () => {
  return (
    <Layout
      title="Mobilify | Convert Your Website or Idea into a Mobile App"
      description="Transform your website or idea into a professional mobile app with AI-powered conversion. No code required. Get iOS and Android builds ready for app stores."
    >
      <HeroSection />
      <HowItWorksSection />
      <FeaturesSection />
      <CTASection />
    </Layout>
  );
};

export default HomePage;
