import React, { useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import PricingCards from '@/components/pricing/PricingCards';
import FAQSection from '@/components/pricing/FAQSection';
import { trackPricingView } from '@/lib/analytics';

/**
 * Pricing page component
 * Implements PRP-Pricing-v1.md prompts 1-4
 */
const PricingPage: React.FC = () => {
  useEffect(() => {
    // Track pricing page view for analytics
    trackPricingView();
  }, []);

  return (
    <Layout
      title="Pricing | Mobilify - Simple, Transparent App Development Pricing"
      description="Choose the perfect plan for your mobile app development needs. One-time payment, no hidden fees. Get your iOS and Android app builds ready for app stores."
    >
      <PricingCards />
      <FAQSection />
    </Layout>
  );
};

export default PricingPage;
